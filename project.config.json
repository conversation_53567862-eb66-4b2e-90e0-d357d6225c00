{"compileType": "miniprogram", "libVersion": "3.121.1", "packOptions": {"ignore": [], "include": []}, "setting": {"minified": false, "urlCheck": false, "useNewDevtools": false, "useNewCompiler": false, "autoAudits": false, "es6": false, "enhance": false, "postcss": false, "minifyWXSS": false, "minifyWXML": false}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "68875929a6ff880001b8c165", "projectname": "微健通", "miniprogramRoot": "."}