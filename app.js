// 微健通小程序入口文件
// 调试信息：检查环境
console.log('=== 环境调试信息 ===')
console.log('typeof require:', typeof require)
console.log('typeof module:', typeof module)
console.log('typeof exports:', typeof exports)
console.log('typeof __webpack_require__:', typeof __webpack_require__)
console.log('typeof xhs:', typeof xhs)
console.log('===================')

App({
  onLaunch(options) {
    console.log('微健通启动')
    console.log('启动参数:', options)
    this.initApp()
  },

  onShow(options) {
    console.log('应用显示')
    console.log('显示参数:', options)
  },

  onHide() {
    console.log('应用隐藏')
  },

  onError(msg) {
    console.error('应用错误:', msg)
    console.error('错误堆栈:', new Error().stack)
  },

  initApp() {
    console.log('应用初始化完成')
    
    // 获取用户信息
    try {
      const userInfo = xhs.getStorageSync('userInfo')
      if (userInfo) {
        this.globalData.userInfo = userInfo
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  },

  getUserInfo() {
    return this.globalData.userInfo
  },

  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo
    try {
      xhs.setStorageSync('userInfo', userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  },

  globalData: {
    userInfo: null,
    systemInfo: null,
    appVersion: '1.0.0'
  }
})
