// 微健通小程序入口文件
App({
  onLaunch(options) {
    console.log('微健通启动')
    this.initApp()
  },

  onShow(options) {
    console.log('应用显示')
  },

  onHide() {
    console.log('应用隐藏')
  },

  onError(msg) {
    console.error('应用错误:', msg)
  },

  initApp() {
    console.log('应用初始化完成')
    
    // 获取用户信息
    try {
      const userInfo = xhs.getStorageSync('userInfo')
      if (userInfo) {
        this.globalData.userInfo = userInfo
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  },

  getUserInfo() {
    return this.globalData.userInfo
  },

  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo
    try {
      xhs.setStorageSync('userInfo', userInfo)
    } catch (error) {
      console.error('保存用户信息失败:', error)
    }
  },

  globalData: {
    userInfo: null,
    systemInfo: null,
    appVersion: '1.0.0'
  }
})
