/**
 * 我的预约页面
 * 功能：显示用户的预约记录，支持查看详情和取消预约
 */

// 移除 require 导入，使用本地数据

Page({
  data: {
    loading: true,
    appointments: [],
    stats: {
      total: 0,
      pending: 0,
      confirmed: 0,
      completed: 0,
      cancelled: 0
    }
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('我的预约页面加载')
    this.loadAppointments()
  },

  /**
   * 页面显示时刷新数据
   */
  onShow() {
    this.loadAppointments()
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    var that = this
    this.loadAppointments(function() {
      xhs.stopPullDownRefresh()
    })
  },

  /**
   * 加载预约列表
   */
  loadAppointments: function(callback) {
    var that = this
    try {
      this.setData({ loading: true })

      // 使用模拟数据
      var mockAppointments = [
        {
          id: 1,
          appointmentNumber: 'WJT20250102001',
          name: '张三',
          appointmentType: '白癜风',
          selectedDate: '2025年01月15日',
          timeSlot: '上午',
          status: 'confirmed',
          createdAt: '2025-01-02T10:30:00.000Z'
        },
        {
          id: 2,
          appointmentNumber: 'WJT20250103002',
          name: '李四',
          appointmentType: '皮肤病',
          selectedDate: '2025年01月16日',
          timeSlot: '下午',
          status: 'cancelled',
          createdAt: '2025-01-03T09:15:00.000Z'
        }
      ]

      // 格式化预约数据
      var formattedAppointments = this.formatAppointments(mockAppointments)

      this.setData({
        appointments: formattedAppointments,
        stats: {
          total: 2,
          pending: 1,
          confirmed: 1,
          completed: 0,
          cancelled: 0
        }
      })

      if (callback) callback()

    } catch (error) {
      console.error('加载预约列表失败:', error)
      xhs.showToast({
        title: '加载失败',
        icon: 'none'
      })
      if (callback) callback()
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 格式化预约数据
   */
  formatAppointments: function(appointments) {
    var statusMap = {
      confirmed: { label: '已成功', color: '#07c160' },
      completed: { label: '已完成', color: '#10aeff' },
      cancelled: { label: '已取消', color: '#fa5151' }
    }

    var that = this
    var result = []
    for (var i = 0; i < appointments.length; i++) {
      var appointment = appointments[i]
      var status = statusMap[appointment.status] || statusMap.pending

      var newAppointment = {}
      for (var key in appointment) {
        newAppointment[key] = appointment[key]
      }
      newAppointment.statusText = status.label
      newAppointment.statusColor = status.color
      newAppointment.createTimeText = that.formatDateTime(appointment.createdAt)

      result.push(newAppointment)
    }
    return result
  },

  /**
   * 格式化日期时间
   */
  formatDateTime: function(dateTimeString) {
    if (!dateTimeString) return ''

    var date = new Date(dateTimeString)
    var year = date.getFullYear()
    var month = (date.getMonth() + 1).toString()
    if (month.length === 1) month = '0' + month
    var day = date.getDate().toString()
    if (day.length === 1) day = '0' + day
    var hours = date.getHours().toString()
    if (hours.length === 1) hours = '0' + hours
    var minutes = date.getMinutes().toString()
    if (minutes.length === 1) minutes = '0' + minutes

    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes
  },

  /**
   * 查看预约详情
   */
  viewAppointmentDetail: function(e) {
    var appointmentId = e.currentTarget.dataset.id

    xhs.navigateTo({
      url: '/pages/appointment-detail/index?id=' + appointmentId
    })
  },

  /**
   * 取消预约
   */
  cancelAppointment: function(e) {
    var appointmentId = e.currentTarget.dataset.id
    var that = this

    xhs.showModal({
      title: '确认取消',
      content: '确定要取消这个预约吗？取消后不可恢复。',
      success: function(res) {
        if (res.confirm) {
          xhs.showLoading({ title: '取消中...' })

          // 模拟取消延迟
          setTimeout(function() {
            try {
              xhs.showToast({
                title: '取消成功',
                icon: 'success'
              })

              // 刷新列表
              that.loadAppointments()

            } catch (error) {
              console.error('取消预约失败:', error)
              xhs.showToast({
                title: error.message || '取消失败',
                icon: 'none'
              })
            } finally {
              xhs.hideLoading()
            }
          }, 800)
        }
      },
      fail: function(error) {
        console.error('取消预约失败:', error)
        xhs.showToast({
          title: '取消失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 开始预约
   */
  startAppointment() {
    xhs.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '微健通 - 我的预约',
      path: '/pages/appointments/index'
    }
  }
})
