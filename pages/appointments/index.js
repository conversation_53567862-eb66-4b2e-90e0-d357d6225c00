/**
 * 我的预约页面
 * 功能：显示用户的预约记录，支持查看详情和取消预约
 */

// 移除 require 导入，使用本地数据

Page({
  data: {
    loading: true,
    appointments: [],
    stats: {
      total: 0,
      pending: 0,
      confirmed: 0,
      completed: 0,
      cancelled: 0
    }
  },

  /**
   * 页面加载
   */
  async onLoad() {
    console.log('我的预约页面加载')
    await this.loadAppointments()
  },

  /**
   * 页面显示时刷新数据
   */
  onShow() {
    this.loadAppointments()
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.loadAppointments()
    xhs.stopPullDownRefresh()
  },

  /**
   * 加载预约列表
   */
  async loadAppointments() {
    try {
      this.setData({ loading: true })

      // 使用模拟数据
      const mockAppointments = [
        {
          id: 1,
          appointmentNumber: 'WJT20250102001',
          name: '张三',
          appointmentType: '白癜风',
          selectedDate: '2025年01月15日',
          timeSlot: '上午',
          status: 'confirmed',
          createdAt: '2025-01-02T10:30:00.000Z'
        },
        {
          id: 2,
          appointmentNumber: 'WJT20250103002',
          name: '李四',
          appointmentType: '皮肤病',
          selectedDate: '2025年01月16日',
          timeSlot: '下午',
          status: 'cancelled',
          createdAt: '2025-01-03T09:15:00.000Z'
        }
      ]

      // 格式化预约数据
      const formattedAppointments = this.formatAppointments(mockAppointments)

      this.setData({
        appointments: formattedAppointments,
        stats: {
          total: 2,
          pending: 1,
          confirmed: 1,
          completed: 0,
          cancelled: 0
        }
      })

    } catch (error) {
      console.error('加载预约列表失败:', error)
      xhs.showToast({
        title: '加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 格式化预约数据
   */
  formatAppointments(appointments) {
    const statusMap = {
      confirmed: { label: '已成功', color: '#07c160' },
      completed: { label: '已完成', color: '#10aeff' },
      cancelled: { label: '已取消', color: '#fa5151' }
    }

    return appointments.map(appointment => {
      const status = statusMap[appointment.status] || statusMap.pending

      return {
        ...appointment,
        statusText: status.label,
        statusColor: status.color,
        createTimeText: this.formatDateTime(appointment.createdAt)
      }
    })
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateTimeString) {
    if (!dateTimeString) return ''

    const date = new Date(dateTimeString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  /**
   * 查看预约详情
   */
  viewAppointmentDetail(e) {
    const appointmentId = e.currentTarget.dataset.id

    xhs.navigateTo({
      url: `/pages/appointment-detail/index?id=${appointmentId}`
    })
  },

  /**
   * 取消预约
   */
  async cancelAppointment(e) {
    const appointmentId = e.currentTarget.dataset.id

    try {
      const result = await new Promise((resolve, reject) => {
        xhs.showModal({
          title: '确认取消',
          content: '确定要取消这个预约吗？取消后不可恢复。',
          success: (res) => {
            if (res.confirm) {
              resolve(true)
            } else {
              resolve(false)
            }
          },
          fail: reject
        })
      })

      if (!result) return

      xhs.showLoading({ title: '取消中...' })

      // 模拟取消延迟
      await new Promise(resolve => setTimeout(resolve, 800))

      xhs.showToast({
        title: '取消成功',
        icon: 'success'
      })

      // 刷新列表
      this.loadAppointments()

    } catch (error) {
      console.error('取消预约失败:', error)
      xhs.showToast({
        title: error.message || '取消失败',
        icon: 'none'
      })
    } finally {
      xhs.hideLoading()
    }
  },

  /**
   * 开始预约
   */
  startAppointment() {
    xhs.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '微健通 - 我的预约',
      path: '/pages/appointments/index'
    }
  }
})
