<!--pages/appointments/index.xhsml-->
<view class="appointments-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 预约列表 -->
  <view wx:elif="{{appointments.length > 0}}" class="appointment-list">
    <view
      wx:for="{{appointments}}"
      wx:key="id"
      class="appointment-item"
      bindtap="viewAppointmentDetail"
      data-id="{{item.id}}"
    >
    <view class="create-time">—— {{item.createTimeText}} ——</view>
    <view class="appointment-content">
      <view class="appointment-icon"></view>
        <view class="appointment-info">
          <view class="info-row">
            <text class="info-value">{{item.name}}</text>
          </view>
          <view class="appointments-time">预约时间：{{item.selectedDate}}</view>
        </view>
        <view class="btn-status">{{item.status}}</view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <view class="empty-icon">📅</view>
    <text class="empty-text">暂无预约记录</text>
    <button class="start-appointment-btn" type="primary" bindtap="startAppointment">
      立即预约
    </button>
  </view>
</view>