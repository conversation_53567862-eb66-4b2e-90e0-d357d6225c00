/**
 * 医生详情页面
 * 功能：显示医生的详细信息，支持预约咨询
 */

// 移除 require 导入，使用本地数据

Page({
  data: {
    loading: true,
    doctor: null,
    doctorId: null
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    const doctorId = options.id
    if (!doctorId) {
      xhs.showToast({
        title: '医生ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        xhs.navigateBack()
      }, 1500)
      return
    }

    this.setData({ doctorId })
    this.loadDoctorDetail()
  },

  /**
   * 加载医生详情
   */
  loadDoctorDetail() {
    try {
      this.setData({ loading: true })

      // 使用模拟医生数据
      const mockDoctors = [
        {
          id: 1,
          name: '张安平',
          title: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院',
          avatar: '../../images/d-1.png',
          specialty: ['白癜风', '银屑病', '湿疹'],
          experience: '从医20年',
          introduction: '擅长各种皮肤病的诊断和治疗，特别是白癜风的综合治疗。',
          rating: 4.9,
          consultCount: 1580
        },
        {
          id: 2,
          name: '李医生',
          title: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院',
          avatar: '../../images/d-2.png',
          specialty: ['痤疮', '脱发', '过敏性皮炎'],
          experience: '从医15年',
          introduction: '专注于青少年皮肤问题的治疗和预防。',
          rating: 4.8,
          consultCount: 1200
        }
      ]

      const doctor = mockDoctors.find(d => d.id == this.data.doctorId) || mockDoctors[0]

      // 格式化医生信息
      const formattedDoctor = {
        ...doctor,
        displayRating: doctor.rating ? doctor.rating.toFixed(1) : '暂无评分',
        displayConsultCount: doctor.consultCount ? `${doctor.consultCount}人咨询` : '暂无数据',
        specialtyText: doctor.specialty ? doctor.specialty.join('、') : '暂无专科信息'
      }

      this.setData({ doctor: formattedDoctor })

    } catch (error) {
      console.error('加载医生详情失败:', error)
      xhs.showToast({
        title: '加载失败',
        icon: 'none'
      })

      setTimeout(() => {
        xhs.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 预约医生
   */
  appointmentDoctor() {
    // 跳转到首页并传递医生信息
    xhs.switchTab({
      url: '/pages/home/<USER>'
    })
    
    // 可以通过全局数据或事件总线传递医生信息
    const app = getApp()
    app.globalData.selectedDoctor = this.data.doctor
  },

  /**
   * 在线咨询
   */
  onlineConsult() {
    xhs.showModal({
      title: '在线咨询',
      content: '在线咨询功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 查看医生排班
   */
  viewSchedule() {
    xhs.showModal({
      title: '医生排班',
      content: '医生排班功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 预览医生头像
   */
  previewAvatar() {
    if (this.data.doctor && this.data.doctor.avatar) {
      xhs.previewImage({
        urls: [this.data.doctor.avatar],
        current: this.data.doctor.avatar
      })
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const doctor = this.data.doctor
    return {
      title: `${doctor ? doctor.name : '医生'} - 微健通`,
      path: `/pages/doctor-detail/index?id=${this.data.doctorId}`,
      imageUrl: doctor ? doctor.avatar : ''
    }
  }
})
