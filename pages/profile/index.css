/* pages/profile/index.css */
.profile-page {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 用户信息头部 */
.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx 60rpx;
  background: linear-gradient(135deg, #de202c 0%, #ff6b6b 100%);
  color: white;
  position: relative;
}

.avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-bottom: 32rpx;
}

.username {
  font-size: 36rpx;
  font-weight: 500;
}

/* 统计数据 */
.stats-section {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: #fff;
  padding: 48rpx 24rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.stats-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #de202c;
}

.stats-label {
  font-size: 26rpx;
  color: #666;
}

.stats-divider {
  width: 2rpx;
  height: 60rpx;
  background: #f0f0f0;
}

/* 菜单区域 */
.menu-section {
  background: #fff;
  padding:0 48rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #f8f9fa;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.menu-title {
  flex: 1;
  font-size: 32rpx;
  color: #323233;
}

.menu-badge {
  background: #de202c;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 16rpx;
  min-width: 32rpx;
  text-align: center;
}

.menu-arrow {
  font-size: 32rpx;
  color: #969799;
}

.menu-divider {
  height: 16rpx;
  background: #f7f8fa;
  margin: 0 -32rpx;
}
