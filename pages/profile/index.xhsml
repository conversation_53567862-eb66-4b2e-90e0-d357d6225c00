<!--pages/profile/index.xhsml-->
<view class="profile-page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <view wx:else>
    <!-- 用户信息头部 -->
    <view class="profile-header">
      <image
        class="avatar"
        src="{{userInfo.avatar || 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg'}}"
        mode="aspectFill"
      ></image>
      <text class="username">{{userInfo.nickname || '小红书用户'}}</text>
    </view>

    <!-- 统计数据 -->
    <view class="stats-section">
      <view class="stats-item" bindtap="goToAppointments">
        <text class="stats-number">{{stats.appointments}}</text>
        <text class="stats-label">我的预约</text>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item" bindtap="goToCoupons">
        <text class="stats-number">{{stats.coupons}}</text>
        <text class="stats-label">可用优惠券</text>
      </view>
    </view>
  </view>
</view>
