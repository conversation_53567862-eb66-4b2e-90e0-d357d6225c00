/**
 * 首页
 * 功能：展示医生信息、优惠券、预约表单
 * 重构：将复杂逻辑拆分到服务层
 */

// 移除 require 导入，直接在页面中实现功能

// 调试信息：检查页面环境
console.log('=== 首页环境调试信息 ===')
console.log('typeof require:', typeof require)
console.log('typeof module:', typeof module)
console.log('typeof exports:', typeof exports)
console.log('typeof __webpack_require__:', typeof __webpack_require__)
console.log('typeof xhs:', typeof xhs)
console.log('typeof Page:', typeof Page)
console.log('========================')

Page({
  /**
   * 页面数据
   */
  data: {
    // 页面加载状态
    loading: true,

    // 表单数据
    formData: {
      name: '',
      phone: '',
      age: '',
      gender: 'female',
      appointmentType: 'vitiligo',
      selectedDate: '',
      timeSlot: ''
    },
    // 时间段选择相关
    showTimeSlot: false,

    // 日期选择器相关
    currentDate: '',
    minDate: '',
    maxDate: '',

    // 业务数据 - 保留初始数据，不重复定义
    coupons: [
        {
          id: 1,
          title: '美国进口308光斑',
          description: '免费体验6个',
          tag: '（限初诊）',
          received: true  // 已领取
        },
        {
          id: 2,
          title: '白癜风6维32项',
          description: '0元',
          tag: '基础病因筛查',
          received: true  // 已领取
        },
        {
          id: 3,
          title: '美国三维皮肤CT检测',
          description: '78元',
          tag: '（半价）',
          received: false  // 未领取
        },
        {
          id: 4,
          title: '皮肤镜专业检测',
          description: '半价',
          tag: '（原价360）',
          received: true  // 已领取
        },
        {
          id: 5,
          title: '过敏源检测',
          description: '半价',
          tag: '（原价270）',
          received: false  // 未领取
        }
      ],
      doctors:[
        {
          id: 1,
          name: '张安平',
          title: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院',
          avatar: '../../images/d-1.png'
        },
        {
          id: 2,
          name: '李医生',
          title: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院',
          avatar: '../../images/d-2.png'
        },
        {
          id: 3,
          name: '王医生',
          title: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院',
          avatar: '../../images/d-3.png'
        },
        {
          id: 4,
          name: '赵医生',
          title: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院',
          avatar: '../../images/d-4.png'
        },
        {
          id: 5,
          name: '刘医生',
          title: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院',
          avatar: '../../images/d-5.png'
        }
      ],

    // 提交状态
    submitting: false
  },

  /**
   * 页面加载
   */
  onLoad() {
    console.log('首页加载')

    try {
      // 初始化页面数据 - 添加条件判断，如果服务不可用则使用本地数据
      this.initPageData()

      // 初始化日期选择器
      this.initDatePicker()

      // 加载最后一次预约信息
      this.loadLastAppointment()

    } catch (error) {
      console.error('页面初始化失败:', error)
      xhs.showToast({
        title: '页面加载失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 初始化页面数据
   */
  initPageData() {
    // 确保数据已正确加载
    this.ensureDataLoaded()
    console.log('数据初始化完成 - 优惠券:', this.data.coupons.length, '医生:', this.data.doctors.length)
  },

  /**
   * 确保数据已加载（真机兼容性处理）
   */
  ensureDataLoaded() {
    // 检查优惠券数据
    if (!this.data.coupons || this.data.coupons.length === 0) {
      console.log('重新设置优惠券数据')
      this.setData({
        coupons: [
          {
            id: 1,
            title: '美国进口308光斑',
            description: '免费体验6个',
            tag: '（限初诊）',
            received: true
          },
          {
            id: 2,
            title: '白癜风6维32项',
            description: '0元',
            tag: '基础病因筛查',
            received: true
          },
          {
            id: 3,
            title: '美国三维皮肤CT检测',
            description: '78元',
            tag: '（半价）',
            received: false
          },
          {
            id: 4,
            title: '皮肤镜专业检测',
            description: '半价',
            tag: '（原价360）',
            received: true
          },
          {
            id: 5,
            title: '过敏源检测',
            description: '半价',
            tag: '（原价270）',
            received: false
          }
        ]
      })
    }

    // 检查医生数据
    if (!this.data.doctors || this.data.doctors.length === 0) {
      console.log('重新设置医生数据')
      this.setData({
        doctors: [
          {
            id: 1,
            name: '张安平',
            title: '皮肤科主任医师',
            hospital: '安徽医科大学第一附属医院',
            avatar: '../../images/d-1.png'
          },
          {
            id: 2,
            name: '李医生',
            title: '皮肤科主任医师',
            hospital: '安徽医科大学第一附属医院',
            avatar: '../../images/d-2.png'
          },
          {
            id: 3,
            name: '王医生',
            title: '皮肤科主任医师',
            hospital: '安徽医科大学第一附属医院',
            avatar: '../../images/d-3.png'
          },
          {
            id: 4,
            name: '赵医生',
            title: '皮肤科主任医师',
            hospital: '安徽医科大学第一附属医院',
            avatar: '../../images/d-4.png'
          },
          {
            id: 5,
            name: '刘医生',
            title: '皮肤科主任医师',
            hospital: '安徽医科大学第一附属医院',
            avatar: '../../images/d-5.png'
          }
        ]
      })
    }
  },

  // 其他方法保持不变...
  /**
   * 页面显示
   */
  onShow() {
    // 每次显示页面时确保数据正确
    console.log('页面显示，检查数据状态')
    this.ensureDataLoaded()

    // 强制更新页面
    this.setData({
      loading: false
    })
  },

  onReady() {
    // 页面初次渲染完成时再次确保数据正确
    console.log('页面渲染完成，最终检查数据')
    this.ensureDataLoaded()
  },

  /**
   * 刷新数据
   */
  refreshData() {
    // 使用本地数据，无需刷新
    console.log('数据刷新完成')
  },

  /**
   * 初始化日期选择器
   */
  initDatePicker: function() {
    var today = new Date()
    var year = today.getFullYear()
    var month = (today.getMonth() + 1).toString()
    if (month.length === 1) month = '0' + month
    var day = today.getDate().toString()
    if (day.length === 1) day = '0' + day

    var currentDateStr = year + '-' + month + '-' + day
    var maxDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)
    var maxMonth = (maxDate.getMonth() + 1).toString()
    if (maxMonth.length === 1) maxMonth = '0' + maxMonth
    var maxDay = maxDate.getDate().toString()
    if (maxDay.length === 1) maxDay = '0' + maxDay
    var maxDateStr = maxDate.getFullYear() + '-' + maxMonth + '-' + maxDay
    var displayDate = year + '年' + month + '月' + day + '日'

    var currentHour = today.getHours()
    var defaultTimeSlot = currentHour < 11 ? '上午' : '下午'

    this.setData({
      'formData.selectedDate': displayDate,
      'formData.timeSlot': defaultTimeSlot,
      currentDate: currentDateStr,
      minDate: currentDateStr,
      maxDate: maxDateStr,
      showTimeSlot: true
    })
  },

  /**
   * 加载最后一次预约信息
   */
  loadLastAppointment: function() {
    try {
      var lastAppointment = xhs.getStorageSync('lastAppointment')
      if (lastAppointment) {
        this.setData({
          'formData.name': lastAppointment.name || '',
          'formData.age': lastAppointment.age || '',
          'formData.gender': lastAppointment.gender === '男' ? 'male' : 'female'
        })
      }
    } catch (error) {
      console.error('获取预约信息失败:', error)
    }
  },
  /**
   * 表单输入处理
   */
  onNameInput(e) {
    this.setData({ 'formData.name': e.detail.value })
  },

  onPhoneInput(e) {
    this.setData({ 'formData.phone': e.detail.value })
  },

  onAgeInput(e) {
    this.setData({ 'formData.age': e.detail.value })
  },

  /**
   * 性别选择
   */
  onGenderChange(e) {
    this.setData({ 'formData.gender': e.currentTarget.dataset.value })
  },

  /**
   * 预约类型选择
   */
  onAppointmentTypeChange(e) {
    const appointmentType = e.currentTarget.dataset.value
    this.setData({ 'formData.appointmentType': appointmentType })

    // 根据预约类型刷新推荐医生
    this.loadRecommendedDoctors(appointmentType)
  },

  /**
   * 日期选择
   */
  onDatePickerChange(e) {
    const dateValue = e.detail.value
    const [year, month, day] = dateValue.split('-')
    const displayDate = `${year}年${month}月${day}日`

    // 如果选择的是今天，根据当前时间自动选择时间段
    const today = new Date()
    const selectedDate = new Date(year, month - 1, day)
    const isToday = selectedDate.toDateString() === today.toDateString()

    let defaultTimeSlot = ''
    if (isToday) {
      const currentHour = today.getHours()
      defaultTimeSlot = currentHour < 11 ? '上午' : '下午'
    } else {
      defaultTimeSlot = '上午'
    }

    this.setData({
      'formData.selectedDate': displayDate,
      'formData.timeSlot': defaultTimeSlot,
      currentDate: dateValue,
      showTimeSlot: true
    })
  },

  /**
   * 时间段选择
   */
  onTimeSlotChange(e) {
    this.setData({
      'formData.timeSlot': e.currentTarget.dataset.value
    })
  },

  /**
   * 加载推荐医生
   */
  loadRecommendedDoctors(appointmentType) {
    // 使用本地数据，无需重新加载
    console.log('推荐医生类型:', appointmentType)
  },

  /**
   * 医生咨询点击
   */
  consultDoctor(e) {
    const doctorId = e.currentTarget.dataset.id

    xhs.navigateTo({
      url: `/pages/doctor-detail/index?id=${doctorId}`
    })
  },

  /**
   * 优惠券领取
   */
  receiveCoupon(e) {
    const couponId = e.currentTarget.dataset.id

    // 检查是否已领取
    const coupon = this.data.coupons.find(c => c.id == couponId)
    if (coupon && coupon.received) {
      xhs.showToast({
        title: '已领取过该优惠券',
        icon: 'none'
      })
      return
    }

    const that = this
    xhs.showLoading({ title: '领取中...' })

    // 模拟网络请求延迟
    setTimeout(function() {
      try {
        // 更新优惠券状态
        const updatedCoupons = that.data.coupons.map(c => {
          if (c.id == couponId) {
            return { ...c, received: true }
          }
          return c
        })

        that.setData({ coupons: updatedCoupons })

        xhs.showToast({
          title: '领取成功',
          icon: 'success'
        })

        console.log('优惠券领取成功，ID:', couponId)

      } catch (error) {
        console.error('领取优惠券失败:', error)
        xhs.showToast({
          title: '领取失败',
          icon: 'none'
        })
      } finally {
        xhs.hideLoading()
      }
    }, 800)
  },

  /**
   * 获取小红书手机号
   */
  onGetPhoneNumber(e) {
    console.log('手机号授权结果：', e.detail)

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 用户授权成功
      const code = e.detail.code

      if (code) {
        const that = this
        xhs.showLoading({ title: '获取中...' })

        // 模拟获取手机号
        setTimeout(function() {
          try {
            // 模拟随机手机号
            const phones = ['13800138000', '13900139000', '15800158000']
            const randomPhone = phones[Math.floor(Math.random() * phones.length)]

            that.setData({
              'formData.phone': randomPhone
            })

            xhs.showToast({
              title: '手机号获取成功',
              icon: 'success'
            })

          } catch (error) {
            console.error('获取手机号失败:', error)
            xhs.showToast({
              title: '获取失败，请手动输入',
              icon: 'none'
            })
            that.promptManualInput()
          } finally {
            xhs.hideLoading()
          }
        }, 1000)
      } else {
        xhs.showToast({ title: '获取授权码失败', icon: 'none' })
        this.promptManualInput()
      }
    } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
      // 用户拒绝授权
      xhs.showToast({ title: '您取消了授权', icon: 'none' })
      this.promptManualInput()
    } else {
      // 其他错误
      console.error('获取手机号失败：', e.detail.errMsg)
      xhs.showToast({ title: '获取失败，请手动输入', icon: 'none' })
      this.promptManualInput()
    }
  },

  /**
   * 提示用户手动输入手机号
   */
  promptManualInput() {
    xhs.showModal({
      title: '温馨提示',
      content: '请在下方输入框中手动输入您的手机号码',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { name, phone, appointmentType, selectedDate, timeSlot } = this.data.formData

    if (!name.trim()) {
      xhs.showToast({ title: '请输入姓名', icon: 'none' })
      return false
    }

    if (!phone.trim()) {
      xhs.showToast({ title: '请输入电话', icon: 'none' })
      return false
    }

    // 手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      xhs.showToast({ title: '请输入正确的手机号', icon: 'none' })
      return false
    }

    if (!appointmentType) {
      xhs.showToast({ title: '请选择预约类型', icon: 'none' })
      return false
    }

    if (!selectedDate) {
      xhs.showToast({ title: '请选择日期', icon: 'none' })
      return false
    }

    if (!timeSlot) {
      xhs.showToast({ title: '请选择时间段', icon: 'none' })
      return false
    }

    return true
  },

  /**
   * 表单提交
   */
  onSubmit() {
    // 防止重复提交
    if (this.data.submitting) return

    const that = this
    this.setData({ submitting: true })

    // 表单验证
    if (!this.validateForm()) {
      this.setData({ submitting: false })
      return
    }

    // 模拟提交延迟
    setTimeout(function() {
      try {
        // 模拟提交成功
        const result = {
          appointmentNumber: 'WJT' + Date.now(),
          id: Date.now()
        }

        // 提交成功
        that.handleSubmitSuccess(result)

      } catch (error) {
        console.error('提交预约失败:', error)
        that.handleSubmitError(error)
      } finally {
        that.setData({ submitting: false })
      }
    }, 1200)
  },

  /**
   * 处理提交成功
   */
  handleSubmitSuccess(result) {
    xhs.showModal({
      title: '预约成功',
      content: `您的预约申请已提交成功！\n预约编号：${result.appointmentNumber || ''}\n稍后会有专人和您确认，请耐心等待！`,
      showCancel: false,
      confirmText: '查看预约',
      success: (res) => {
        if (res.confirm) {
          // 跳转到预约列表页面
          xhs.navigateTo({
            url: '/pages/appointments/index'
          })
        }
      }
    })

    // 重置表单
    this.resetForm()
  },

  /**
   * 处理提交失败
   */
  handleSubmitError(error) {
    // 显示验证错误或其他错误
    if (error.message) {
      xhs.showToast({
        title: error.message,
        icon: 'none',
        duration: 2000
      })
    } else {
      xhs.showModal({
        title: '提交失败',
        content: '网络异常或服务暂时不可用，请稍后重试',
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.onSubmit()
          }
        }
      })
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    const displayDate = `${year}年${month}月${day}日`
    const currentHour = today.getHours()
    const defaultTimeSlot = currentHour < 11 ? '上午' : '下午'

    this.setData({
      formData: {
        name: '',
        phone: '',
        age: '',
        gender: 'female',
        appointmentType: 'vitiligo',
        selectedDate: displayDate,
        timeSlot: defaultTimeSlot
      },
      showTimeSlot: true
    })
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '微健通 - 专业皮肤病预约诊疗平台',
      path: '/pages/home/<USER>',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
