/**
 * 预约详情页面
 * 功能：显示预约的详细信息
 */

// 移除 require 导入，使用本地数据

Page({
  data: {
    loading: true,
    appointment: null,
    appointmentId: null
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    const appointmentId = options.id
    if (!appointmentId) {
      xhs.showToast({
        title: '预约ID不能为空',
        icon: 'none'
      })
      setTimeout(() => {
        xhs.navigateBack()
      }, 1500)
      return
    }

    this.setData({ appointmentId })
    this.loadAppointmentDetail()
  },

  /**
   * 加载预约详情
   */
  loadAppointmentDetail() {
    try {
      this.setData({ loading: true })

      // 使用模拟数据
      const mockAppointments = [
        {
          id: 1,
          appointmentNumber: 'WJT20250102001',
          name: '张三',
          phone: '13800138000',
          age: '28',
          gender: '女',
          appointmentType: '白癜风',
          selectedDate: '2025年01月15日',
          timeSlot: '上午',
          status: 'confirmed',
          createdAt: '2025-01-02T10:30:00.000Z',
          doctorName: '张安平',
          doctorTitle: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院'
        },
        {
          id: 2,
          appointmentNumber: 'WJT20250103002',
          name: '李四',
          phone: '13900139000',
          age: '35',
          gender: '男',
          appointmentType: '皮肤病',
          selectedDate: '2025年01月16日',
          timeSlot: '下午',
          status: 'cancelled',
          createdAt: '2025-01-03T09:15:00.000Z',
          doctorName: '李医生',
          doctorTitle: '皮肤科主任医师',
          hospital: '安徽医科大学第一附属医院'
        }
      ]

      const appointment = mockAppointments.find(item => item.id == this.data.appointmentId)

      if (!appointment) {
        throw new Error('预约不存在')
      }

      // 格式化预约信息
      const formattedAppointment = this.formatAppointmentDetail(appointment)

      this.setData({ appointment: formattedAppointment })

    } catch (error) {
      console.error('加载预约详情失败:', error)
      xhs.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      })

      setTimeout(() => {
        xhs.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 格式化预约详情
   */
  formatAppointmentDetail(appointment) {
    const statusMap = {
      confirmed: { label: '已成功', color: '#07c160' },
      completed: { label: '已完成', color: '#10aeff' },
      cancelled: { label: '已取消', color: '#fa5151' }
    }

    const status = statusMap[appointment.status] || statusMap.pending

    return {
      ...appointment,
      statusText: status.label,
      statusColor: status.color,
      createTimeText: this.formatDateTime(appointment.createdAt),
      genderText: appointment.gender === 'male' ? '男' : '女'
    }
  },

  /**
   * 格式化日期时间
   */
  formatDateTime(dateTimeString) {
    if (!dateTimeString) return ''
    
    const date = new Date(dateTimeString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  /**
   * 取消预约
   */
  cancelAppointment() {
    const that = this
    xhs.showModal({
      title: '确认取消',
      content: '确定要取消这个预约吗？取消后不可恢复。',
      success: function(res) {
        if (res.confirm) {
          xhs.showLoading({ title: '取消中...' })

          // 模拟取消延迟
          setTimeout(function() {
            try {
              xhs.showToast({
                title: '取消成功',
                icon: 'success'
              })

              // 刷新详情
              that.loadAppointmentDetail()

            } catch (error) {
              console.error('取消预约失败:', error)
              xhs.showToast({
                title: error.message || '取消失败',
                icon: 'none'
              })
            } finally {
              xhs.hideLoading()
            }
          }, 800)
        }
      },
      fail: function(error) {
        console.error('取消预约失败:', error)
        xhs.showToast({
          title: '取消失败',
          icon: 'none'
        })
      }
    })
  },
  
  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '微健通 - 预约详情',
      path: `/pages/appointment-detail/index?id=${this.data.appointmentId}`
    }
  }
})
